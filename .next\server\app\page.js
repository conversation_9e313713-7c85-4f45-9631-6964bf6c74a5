/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNQeXRob24lNUNXZWJzaXRlJTVDRnJvbTIxc3QuZGV2JTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDUHl0aG9uJTVDV2Vic2l0ZSU1Q0Zyb20yMXN0LmRldiZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsd0lBQXFGO0FBQzVHO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBLHlCQUF5Qiw0SUFBdUY7QUFDaEgsb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNtYXJ0aG9tZS13ZWJzaXRlLz85ZGE0Il0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFsnX19QQUdFX18nLCB7fSwge1xuICAgICAgICAgIHBhZ2U6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFB5dGhvblxcXFxXZWJzaXRlXFxcXEZyb20yMXN0LmRldlxcXFxhcHBcXFxccGFnZS50c3hcIiksIFwiRDpcXFxcUHl0aG9uXFxcXFdlYnNpdGVcXFxcRnJvbTIxc3QuZGV2XFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFB5dGhvblxcXFxXZWJzaXRlXFxcXEZyb20yMXN0LmRldlxcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJEOlxcXFxQeXRob25cXFxcV2Vic2l0ZVxcXFxGcm9tMjFzdC5kZXZcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKSwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCJdLFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkQ6XFxcXFB5dGhvblxcXFxXZWJzaXRlXFxcXEZyb20yMXN0LmRldlxcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp%5Cglobals.css&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp%5Cglobals.css&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp.tsx&server=true!":
/*!***************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp.tsx&server=true! ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app.tsx */ \"(ssr)/./app.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q1B5dGhvbiU1Q1dlYnNpdGUlNUNGcm9tMjFzdC5kZXYlNUNhcHAudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNtYXJ0aG9tZS13ZWJzaXRlLz82ZGJkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHl0aG9uXFxcXFdlYnNpdGVcXFxcRnJvbTIxc3QuZGV2XFxcXGFwcC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app.tsx":
/*!*****************!*\
  !*** ./app.tsx ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-motion-value.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/animation/animate.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-motion-template.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Brain,Briefcase,CheckCircle,Eye,Globe,Headphones,Home,Lock,Mail,Phone,Send,Settings,Smartphone,Star,TrendingUp,Users,Wifi!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(ssr)/./components/ui/avatar.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n// Testimonial Card Component\nfunction TestimonialCard({ author, text, href, className }) {\n    const Card = href ? \"a\" : \"div\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        ...href ? {\n            href\n        } : {},\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"flex flex-col rounded-lg border-t\", \"bg-gradient-to-b from-muted/50 to-muted/10\", \"p-4 text-start sm:p-6\", \"hover:from-muted/60 hover:to-muted/20\", \"max-w-[320px] sm:max-w-[320px]\", \"transition-colors duration-300\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                        className: \"h-12 w-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                            src: author.avatar,\n                            alt: author.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-md font-semibold leading-none\",\n                                children: author.name\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: author.handle\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"sm:text-md mt-4 text-sm text-muted-foreground\",\n                children: text\n            }, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n// Testimonials Section Component\nfunction TestimonialsSection({ title, description, testimonials, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"bg-background text-foreground\", \"py-12 sm:py-24 md:py-32 px-0\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto flex max-w-7xl flex-col items-center gap-4 text-center sm:gap-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center gap-4 px-4 sm:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"max-w-[720px] text-3xl font-semibold leading-tight sm:text-5xl sm:leading-tight\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-md max-w-[600px] font-medium text-muted-foreground sm:text-xl\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex w-full flex-col items-center justify-center overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group flex overflow-hidden p-2 [--gap:1rem] [gap:var(--gap)] flex-row [--duration:40s]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex shrink-0 justify-around [gap:var(--gap)] animate-marquee flex-row group-hover:[animation-play-state:paused]\",\n                                children: [\n                                    ...Array(4)\n                                ].map((_, setIndex)=>testimonials.map((testimonial, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialCard, {\n                                            ...testimonial\n                                        }, `${setIndex}-${i}`, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pointer-events-none absolute inset-y-0 left-0 hidden w-1/3 bg-gradient-to-r from-background sm:block\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pointer-events-none absolute inset-y-0 right-0 hidden w-1/3 bg-gradient-to-l from-background sm:block\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n// Navigation Bar Component\nfunction NavBar({ items, className }) {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(items[0].name);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        handleResize();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"fixed bottom-0 sm:top-0 left-1/2 -translate-x-1/2 z-50 mb-6 sm:pt-6\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3 bg-background/5 border border-border backdrop-blur-lg py-1 px-1 rounded-full shadow-lg\",\n            children: items.map((item)=>{\n                const Icon = item.icon;\n                const isActive = activeTab === item.name;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: item.url,\n                    onClick: ()=>setActiveTab(item.name),\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-colors\", \"text-foreground/80 hover:text-primary\", isActive && \"bg-muted text-primary\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden md:inline\",\n                            children: item.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                size: 18,\n                                strokeWidth: 2.5\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 15\n                        }, this),\n                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            layoutId: \"lamp\",\n                            className: \"absolute inset-0 w-full bg-primary/5 rounded-full -z-10\",\n                            initial: false,\n                            transition: {\n                                type: \"spring\",\n                                stiffness: 300,\n                                damping: 30\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-primary rounded-t-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute w-12 h-6 bg-primary/20 rounded-full blur-md -top-2 -left-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute w-8 h-6 bg-primary/20 rounded-full blur-md -top-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute w-4 h-4 bg-primary/20 rounded-full blur-sm top-0 left-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, item.name, true, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 184,\n        columnNumber: 5\n    }, this);\n}\n// Contact Section Component\nfunction ContactSection({ title = \"Li\\xean hệ với ch\\xfang t\\xf4i\", description = \"Ch\\xfang t\\xf4i sẵn s\\xe0ng tư vấn v\\xe0 hỗ trợ bạn ph\\xe1t triển giải ph\\xe1p AI v\\xe0 SmartHome ph\\xf9 hợp nhất.\", phone = \"(+84) 123-456-789\", email = \"<EMAIL>\", web = {\n    label: \"aismarthome.vn\",\n    url: \"https://aismarthome.vn\"\n} }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        message: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: \"\"\n                }));\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = \"Họ t\\xean l\\xe0 bắt buộc\";\n        }\n        if (!formData.email.trim()) {\n            newErrors.email = \"Email l\\xe0 bắt buộc\";\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = \"Vui l\\xf2ng nhập email hợp lệ\";\n        }\n        if (!formData.message.trim()) {\n            newErrors.message = \"Tin nhắn l\\xe0 bắt buộc\";\n        } else if (formData.message.trim().length < 10) {\n            newErrors.message = \"Tin nhắn phải c\\xf3 \\xedt nhất 10 k\\xfd tự\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setIsSubmitting(true);\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        setIsSubmitting(false);\n        setIsSubmitted(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-32 bg-gradient-to-br from-slate-50 to-blue-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-auto flex max-w-screen-xl flex-col justify-between gap-10 lg:flex-row lg:gap-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto flex max-w-sm flex-col justify-between gap-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"mb-2 text-5xl font-semibold lg:mb-1 lg:text-6xl text-slate-900\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-fit lg:mx-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"mb-6 text-center text-2xl font-semibold lg:text-left text-slate-900\",\n                                        children: \"Th\\xf4ng tin li\\xean hệ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"ml-4 list-disc space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: \"Điện thoại: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: `tel:${phone}`,\n                                                        className: \"text-blue-600 hover:underline\",\n                                                        children: phone\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: \"Email: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: `mailto:${email}`,\n                                                        className: \"text-blue-600 hover:underline\",\n                                                        children: email\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-bold\",\n                                                        children: \"Website: \"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: web.url,\n                                                        target: \"_blank\",\n                                                        className: \"text-blue-600 hover:underline\",\n                                                        children: web.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto flex max-w-screen-md flex-col gap-6 rounded-lg border border-slate-200 bg-white p-10 shadow-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.AnimatePresence, {\n                            mode: \"wait\",\n                            children: !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.form, {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                initial: {\n                                    opacity: 1\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: -20\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid w-full items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Họ v\\xe0 t\\xean\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        type: \"text\",\n                                                        id: \"name\",\n                                                        placeholder: \"Nhập họ v\\xe0 t\\xean\",\n                                                        value: formData.name,\n                                                        onChange: (e)=>handleInputChange(\"name\", e.target.value),\n                                                        className: errors.name ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm\",\n                                                        children: errors.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid w-full items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                        htmlFor: \"phone\",\n                                                        children: \"Số điện thoại\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                        type: \"tel\",\n                                                        id: \"phone\",\n                                                        placeholder: \"Nhập số điện thoại\",\n                                                        value: formData.phone,\n                                                        onChange: (e)=>handleInputChange(\"phone\", e.target.value)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid w-full items-center gap-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"email\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                type: \"email\",\n                                                id: \"email\",\n                                                placeholder: \"Nhập địa chỉ email\",\n                                                value: formData.email,\n                                                onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                className: errors.email ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 21\n                                            }, this),\n                                            errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.email\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid w-full gap-1.5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"message\",\n                                                children: \"Tin nhắn\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                                placeholder: \"M\\xf4 tả nhu cầu của bạn về AI v\\xe0 SmartHome...\",\n                                                id: \"message\",\n                                                rows: 6,\n                                                value: formData.message,\n                                                onChange: (e)=>handleInputChange(\"message\", e.target.value),\n                                                className: errors.message ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this),\n                                            errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm\",\n                                                children: errors.message\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full bg-blue-600 hover:bg-blue-700\",\n                                        disabled: isSubmitting,\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Đang gửi...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 25\n                                                }, this),\n                                                \"Gửi tin nhắn\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, \"form\", true, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-16 h-16 text-green-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold text-slate-900 mb-4\",\n                                        children: \"Gửi th\\xe0nh c\\xf4ng!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600 mb-6\",\n                                        children: \"Cảm ơn bạn đ\\xe3 li\\xean hệ. Ch\\xfang t\\xf4i sẽ phản hồi trong v\\xf2ng 24 giờ.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>{\n                                            setIsSubmitted(false);\n                                            setFormData({\n                                                name: \"\",\n                                                email: \"\",\n                                                phone: \"\",\n                                                message: \"\"\n                                            });\n                                        },\n                                        variant: \"outline\",\n                                        children: \"Gửi tin nhắn kh\\xe1c\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, \"success\", true, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n            lineNumber: 302,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 301,\n        columnNumber: 5\n    }, this);\n}\n// Hero Section Component\nfunction HeroSection() {\n    const COLORS_TOP = [\n        \"#3B82F6\",\n        \"#1E40AF\",\n        \"#1D4ED8\",\n        \"#2563EB\"\n    ];\n    const color = (0,framer_motion__WEBPACK_IMPORTED_MODULE_16__.useMotionValue)(COLORS_TOP[0]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,framer_motion__WEBPACK_IMPORTED_MODULE_17__.animate)(color, COLORS_TOP, {\n            ease: \"easeInOut\",\n            duration: 10,\n            repeat: Infinity,\n            repeatType: \"mirror\"\n        });\n    }, [\n        color\n    ]);\n    const backgroundImage = framer_motion__WEBPACK_IMPORTED_MODULE_18__.useMotionTemplate`radial-gradient(125% 125% at 50% 0%, #0F172A 50%, ${color})`;\n    const border = framer_motion__WEBPACK_IMPORTED_MODULE_18__.useMotionTemplate`1px solid ${color}`;\n    const boxShadow = framer_motion__WEBPACK_IMPORTED_MODULE_18__.useMotionTemplate`0px 4px 24px ${color}`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.section, {\n        style: {\n            backgroundImage\n        },\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-slate-900 px-4 py-24 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex flex-col items-center text-center max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                            className: \"bg-blue-500/20 text-blue-300 border-blue-500/30 px-4 py-2\",\n                            children: \"✨ C\\xf4ng nghệ AI & SmartHome h\\xe0ng đầu\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h1, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"text-4xl sm:text-6xl md:text-7xl font-bold mb-8 bg-gradient-to-br from-white to-blue-200 bg-clip-text text-transparent leading-tight\",\n                        children: [\n                            \"Ph\\xe1t triển phần mềm\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent\",\n                                children: \"AI & SmartHome\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"text-xl sm:text-2xl text-blue-100 max-w-4xl mb-12 leading-relaxed\",\n                        children: \"Ch\\xfang t\\xf4i chuy\\xean ph\\xe1t triển c\\xe1c giải ph\\xe1p AI th\\xf4ng minh v\\xe0 hệ thống SmartHome hiện đại, gi\\xfap kh\\xe1ch h\\xe0ng c\\xe1 nh\\xe2n tối ưu h\\xf3a cuộc sống v\\xe0 n\\xe2ng cao chất lượng sống.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.6\n                        },\n                        className: \"flex flex-col sm:flex-row gap-6 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                style: {\n                                    border,\n                                    boxShadow\n                                },\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"group relative flex items-center gap-3 rounded-full bg-blue-600 hover:bg-blue-700 px-8 py-4 text-white font-semibold transition-all\",\n                                children: [\n                                    \"Tư vấn miễn ph\\xed\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-5 w-5 transition-transform group-hover:translate-x-1\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.button, {\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"flex items-center gap-3 rounded-full border border-white/20 bg-white/10 backdrop-blur-sm px-8 py-4 text-white font-semibold hover:bg-white/20 transition-all\",\n                                children: [\n                                    \"Xem dự \\xe1n\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 540,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 473,\n        columnNumber: 5\n    }, this);\n}\n// Features Section Component\nfunction FeaturesSection() {\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            title: \"Tr\\xed tuệ nh\\xe2n tạo\",\n            description: \"Ph\\xe1t triển c\\xe1c ứng dụng AI th\\xf4ng minh, học m\\xe1y v\\xe0 xử l\\xfd ng\\xf4n ngữ tự nhi\\xean.\",\n            gradient: \"from-purple-500 to-pink-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            title: \"SmartHome IoT\",\n            description: \"Hệ thống nh\\xe0 th\\xf4ng minh t\\xedch hợp IoT, điều khiển từ xa qua smartphone.\",\n            gradient: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            title: \"Kết nối th\\xf4ng minh\",\n            description: \"T\\xedch hợp c\\xe1c thiết bị th\\xf4ng minh với khả năng kết nối ổn định v\\xe0 bảo mật.\",\n            gradient: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            title: \"Tự động h\\xf3a\",\n            description: \"Tự động h\\xf3a c\\xe1c t\\xe1c vụ h\\xe0ng ng\\xe0y, tiết kiệm thời gian v\\xe0 năng lượng.\",\n            gradient: \"from-orange-500 to-red-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n            title: \"Ph\\xe2n t\\xedch dữ liệu\",\n            description: \"Thu thập v\\xe0 ph\\xe2n t\\xedch dữ liệu để tối ưu h\\xf3a hiệu suất v\\xe0 trải nghiệm.\",\n            gradient: \"from-indigo-500 to-purple-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"],\n            title: \"Bảo mật cao\",\n            description: \"Đảm bảo an to\\xe0n dữ liệu với c\\xe1c biện ph\\xe1p bảo mật ti\\xean tiến nhất.\",\n            gradient: \"from-gray-500 to-slate-500\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl sm:text-5xl font-bold text-slate-900 mb-6\",\n                            children: \"Dịch vụ của ch\\xfang t\\xf4i\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-slate-600 max-w-3xl mx-auto\",\n                            children: \"Ch\\xfang t\\xf4i cung cấp c\\xe1c giải ph\\xe1p c\\xf4ng nghệ ti\\xean tiến, từ AI đến SmartHome, gi\\xfap bạn x\\xe2y dựng cuộc sống th\\xf4ng minh v\\xe0 hiện đại.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            whileHover: {\n                                y: -5\n                            },\n                            className: \"group p-8 bg-white rounded-2xl border border-slate-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                        className: \"w-full h-full text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-slate-900 mb-4\",\n                                    children: feature.title\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 622,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 leading-relaxed\",\n                                    children: feature.description\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n            lineNumber: 591,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 590,\n        columnNumber: 5\n    }, this);\n}\n// Proof Section Component\nfunction ProofSection() {\n    const stats = [\n        {\n            number: \"50+\",\n            label: \"Dự \\xe1n ho\\xe0n th\\xe0nh\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"]\n        },\n        {\n            number: \"100+\",\n            label: \"Kh\\xe1ch h\\xe0ng h\\xe0i l\\xf2ng\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"]\n        },\n        {\n            number: \"99%\",\n            label: \"Tỷ lệ th\\xe0nh c\\xf4ng\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"]\n        },\n        {\n            number: \"24/7\",\n            label: \"Hỗ trợ kh\\xe1ch h\\xe0ng\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-24 bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl sm:text-5xl font-bold text-slate-900 mb-6\",\n                            children: \"Th\\xe0nh t\\xedch của ch\\xfang t\\xf4i\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 651,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-slate-600 max-w-3xl mx-auto\",\n                            children: \"Những con số ấn tượng chứng minh chất lượng dịch vụ v\\xe0 sự tin tưởng từ kh\\xe1ch h\\xe0ng.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 654,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.5\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            className: \"text-center p-8 bg-white rounded-2xl shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                        className: \"w-8 h-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl font-bold text-slate-900 mb-2\",\n                                    children: stat.number\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-slate-600\",\n                                    children: stat.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n            lineNumber: 643,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 642,\n        columnNumber: 5\n    }, this);\n}\n// Footer Component\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-slate-900 text-white py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"AI & SmartHome Solutions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 689,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-300 mb-6 max-w-md\",\n                                    children: \"Ch\\xfang t\\xf4i chuy\\xean ph\\xe1t triển c\\xe1c giải ph\\xe1p AI v\\xe0 SmartHome ti\\xean tiến, gi\\xfap kh\\xe1ch h\\xe0ng c\\xe1 nh\\xe2n tối ưu h\\xf3a cuộc sống th\\xf4ng minh.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 688,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Dịch vụ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-slate-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ph\\xe1t triển AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"SmartHome IoT\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Tự động h\\xf3a\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 709,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Tư vấn c\\xf4ng nghệ\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Li\\xean hệ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-slate-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Email: <EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Phone: (+84) 123-456-789\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Website: aismarthome.vn\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 687,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-slate-700 mt-12 pt-8 text-center text-slate-400\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 AI & SmartHome Solutions. Tất cả quyền được bảo lưu.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 724,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n            lineNumber: 686,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 685,\n        columnNumber: 5\n    }, this);\n}\n// Main Website Component\nfunction AISmartHomeWebsite() {\n    const navItems = [\n        {\n            name: \"Trang chủ\",\n            url: \"#home\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"]\n        },\n        {\n            name: \"Dịch vụ\",\n            url: \"#features\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"]\n        },\n        {\n            name: \"Đ\\xe1nh gi\\xe1\",\n            url: \"#testimonials\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"]\n        },\n        {\n            name: \"Li\\xean hệ\",\n            url: \"#contact\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Brain_Briefcase_CheckCircle_Eye_Globe_Headphones_Home_Lock_Mail_Phone_Send_Settings_Smartphone_Star_TrendingUp_Users_Wifi_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    const testimonials = [\n        {\n            author: {\n                name: \"Nguyễn Văn An\",\n                handle: \"@nguyenvanan\",\n                avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\"\n            },\n            text: \"Hệ thống SmartHome do c\\xf4ng ty ph\\xe1t triển rất tuyệt vời. T\\xf4i c\\xf3 thể điều khiển mọi thứ trong nh\\xe0 từ xa một c\\xe1ch dễ d\\xe0ng.\"\n        },\n        {\n            author: {\n                name: \"Trần Thị B\\xecnh\",\n                handle: \"@tranthibinh\",\n                avatar: \"https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face\"\n            },\n            text: \"Ứng dụng AI gi\\xfap t\\xf4i quản l\\xfd c\\xf4ng việc hiệu quả hơn rất nhiều. Đội ngũ hỗ trợ cũng rất chuy\\xean nghiệp.\"\n        },\n        {\n            author: {\n                name: \"L\\xea Minh Cường\",\n                handle: \"@leminhcuong\",\n                avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\"\n            },\n            text: \"Giải ph\\xe1p tự động h\\xf3a nh\\xe0 th\\xf4ng minh đ\\xe3 thay đổi ho\\xe0n to\\xe0n c\\xe1ch t\\xf4i sống. Tiết kiệm được rất nhiều thời gian v\\xe0 năng lượng.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NavBar, {\n                items: navItems\n            }, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 770,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"home\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {}, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 773,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 772,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FeaturesSection, {}, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 777,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 776,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"proof\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProofSection, {}, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 781,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 780,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TestimonialsSection, {\n                    title: \"Kh\\xe1ch h\\xe0ng n\\xf3i g\\xec về ch\\xfang t\\xf4i\",\n                    description: \"H\\xe0ng trăm kh\\xe1ch h\\xe0ng đ\\xe3 tin tưởng v\\xe0 h\\xe0i l\\xf2ng với dịch vụ của ch\\xfang t\\xf4i\",\n                    testimonials: testimonials\n                }, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 785,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 784,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contact\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContactSection, {}, void 0, false, {\n                    fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                    lineNumber: 793,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 792,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Footer, {}, void 0, false, {\n                fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n                lineNumber: 796,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app.tsx\",\n        lineNumber: 769,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AISmartHomeWebsite);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/avatar.tsx":
/*!**********************************!*\
  !*** ./components/ui/avatar.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_3__.Fallback.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/avatar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 43,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGdXQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc21hcnRob21lLXdlYnNpdGUvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9kYTc5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 15,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOEI7QUFDeUI7QUFDVTtBQUNqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zbWFydGhvbWUtd2Vic2l0ZS8uL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4Pzg4ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ0U7QUFLaEMsTUFBTUUseUJBQVdGLDZDQUFnQixDQUMvQixDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQ3hCLHFCQUNFLDhEQUFDQztRQUNDSCxXQUFXSCw4Q0FBRUEsQ0FDWCx3U0FDQUc7UUFFRkUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSCxTQUFTTSxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLXNtYXJ0aG9tZS13ZWJzaXRlLy4vY29tcG9uZW50cy91aS90ZXh0YXJlYS50c3g/YjgwMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5leHBvcnQgaW50ZXJmYWNlIFRleHRhcmVhUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5UZXh0YXJlYUhUTUxBdHRyaWJ1dGVzPEhUTUxUZXh0QXJlYUVsZW1lbnQ+IHt9XG5cbmNvbnN0IFRleHRhcmVhID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MVGV4dEFyZWFFbGVtZW50LCBUZXh0YXJlYVByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPHRleHRhcmVhXG4gICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgXCJmbGV4IG1pbi1oLVs4MHB4XSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuVGV4dGFyZWEuZGlzcGxheU5hbWUgPSBcIlRleHRhcmVhXCJcblxuZXhwb3J0IHsgVGV4dGFyZWEgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJUZXh0YXJlYSIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsInRleHRhcmVhIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc21hcnRob21lLXdlYnNpdGUvLi9saWIvdXRpbHMudHM/Zjc0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB0eXBlIENsYXNzVmFsdWUsIGNsc3ggfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d578399c0450\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zbWFydGhvbWUtd2Vic2l0ZS8uL2FwcC9nbG9iYWxzLmNzcz9jMTNlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDU3ODM5OWMwNDUwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app.tsx":
/*!*****************!*\
  !*** ./app.tsx ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Python\Website\From21st.dev\app.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"AI & SmartHome Solutions\",\n    description: \"Chuy\\xean ph\\xe1t triển c\\xe1c giải ph\\xe1p AI th\\xf4ng minh v\\xe0 hệ thống SmartHome hiện đại\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"vi\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULDJKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1zbWFydGhvbWUtd2Vic2l0ZS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdBSSAmIFNtYXJ0SG9tZSBTb2x1dGlvbnMnLFxuICBkZXNjcmlwdGlvbjogJ0NodXnDqm4gcGjDoXQgdHJp4buDbiBjw6FjIGdp4bqjaSBwaMOhcCBBSSB0aMO0bmcgbWluaCB2w6AgaOG7hyB0aOG7kW5nIFNtYXJ0SG9tZSBoaeG7h24gxJHhuqFpJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInZpXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../app */ \"(rsc)/./app.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"D:\\\\Python\\\\Website\\\\From21st.dev\\\\app\\\\page.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBdUM7QUFFeEIsU0FBU0M7SUFDdEIscUJBQU8sOERBQUNELDRDQUFrQkE7Ozs7O0FBQzVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktc21hcnRob21lLXdlYnNpdGUvLi9hcHAvcGFnZS50c3g/NzYwMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQUlTbWFydEhvbWVXZWJzaXRlIGZyb20gJy4uL2FwcCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIDxBSVNtYXJ0SG9tZVdlYnNpdGUgLz5cbn1cbiJdLCJuYW1lcyI6WyJBSVNtYXJ0SG9tZVdlYnNpdGUiLCJIb21lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/use-sync-external-store","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CPython%5CWebsite%5CFrom21st.dev&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();