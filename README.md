# AI & SmartHome Website

Một website hiện đại được xây dựng bằng Next.js, TypeScript và Tailwind CSS, giới thiệu về dịch vụ phát triển AI và SmartHome.

## Tính năng

- ✨ Giao diện hiện đại với animations mượt mà
- 📱 Responsive design cho mọi thiết bị
- 🎨 Dark/Light mode support
- 🚀 Performance tối ưu với Next.js 14
- 💼 Các section: Hero, Features, Testimonials, Contact
- 📧 Form liên hệ với validation
- 🎭 Framer Motion animations

## Công nghệ sử dụng

- **Framework**: Next.js 14 với App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Animations**: Framer Motion
- **Icons**: Lucide React

## Cài đặt và chạy

### Yêu cầu hệ thống
- Node.js 18+ 
- npm hoặc yarn

### Cách 1: Sử dụng script tự động (Windows)
```bash
# Chạy file start.bat
start.bat
```

### Cách 2: Chạy thủ công
```bash
# Cài đặt dependencies
npm install

# Chạy development server
npm run dev

# Hoặc build production
npm run build
npm start
```

Mở [http://localhost:3000](http://localhost:3000) để xem website.

## Cấu trúc dự án

```
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Home page
├── components/            # UI Components
│   └── ui/               # Reusable UI components
├── lib/                  # Utilities
│   └── utils.ts          # Helper functions
├── app.tsx               # Main website component
└── package.json          # Dependencies
```

## Tùy chỉnh

### Thay đổi nội dung
- Chỉnh sửa file `app.tsx` để thay đổi nội dung website
- Cập nhật thông tin liên hệ trong `ContactSection`
- Thêm/sửa testimonials trong `testimonials` array

### Thay đổi styling
- Chỉnh sửa `tailwind.config.js` cho theme colors
- Cập nhật `app/globals.css` cho custom styles
- Sử dụng CSS variables trong `:root` selector

### Thêm tính năng
- Tạo components mới trong `components/`
- Thêm pages mới trong `app/`
- Cài đặt thêm dependencies qua `npm install`

## Scripts có sẵn

- `npm run dev` - Chạy development server
- `npm run build` - Build production
- `npm run start` - Chạy production server
- `npm run lint` - Kiểm tra code quality

## Hỗ trợ

Nếu gặp vấn đề, hãy kiểm tra:
1. Node.js version >= 18
2. Tất cả dependencies đã được cài đặt
3. Port 3000 không bị chiếm dụng
4. Kiểm tra console log trong browser

## License

MIT License - Tự do sử dụng cho dự án cá nhân và thương mại.
