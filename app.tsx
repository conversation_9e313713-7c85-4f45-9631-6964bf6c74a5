```tsx
"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence, useMotionTemplate, useMotionValue, animate } from 'framer-motion';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Send, 
  User, 
  MessageSquare, 
  Building, 
  ArrowRight,
  Sparkles,
  CheckCircle,
  Clock,
  Globe,
  Shield,
  Zap,
  Home,
  Briefcase,
  FileText,
  Star,
  Award,
  Users,
  TrendingUp,
  Cpu,
  ShieldCheck,
  Layers,
  Eye,
  Brain,
  Smartphone,
  Wifi,
  Settings,
  BarChart3,
  Lock,
  Headphones
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

// Types
interface TestimonialAuthor {
  name: string;
  handle: string;
  avatar: string;
}

interface TestimonialCardProps {
  author: TestimonialAuthor;
  text: string;
  href?: string;
  className?: string;
}

interface NavItem {
  name: string;
  url: string;
  icon: any;
}

interface NavBarProps {
  items: NavItem[];
  className?: string;
}

// Testimonial Card Component
function TestimonialCard({ 
  author,
  text,
  href,
  className
}: TestimonialCardProps) {
  const Card = href ? 'a' : 'div';
  
  return (
    <Card
      {...(href ? { href } : {})}
      className={cn(
        "flex flex-col rounded-lg border-t",
        "bg-gradient-to-b from-muted/50 to-muted/10",
        "p-4 text-start sm:p-6",
        "hover:from-muted/60 hover:to-muted/20",
        "max-w-[320px] sm:max-w-[320px]",
        "transition-colors duration-300",
        className
      )}
    >
      <div className="flex items-center gap-3">
        <Avatar className="h-12 w-12">
          <AvatarImage src={author.avatar} alt={author.name} />
        </Avatar>
        <div className="flex flex-col items-start">
          <h3 className="text-md font-semibold leading-none">
            {author.name}
          </h3>
          <p className="text-sm text-muted-foreground">
            {author.handle}
          </p>
        </div>
      </div>
      <p className="sm:text-md mt-4 text-sm text-muted-foreground">
        {text}
      </p>
    </Card>
  );
}

// Testimonials Section Component
function TestimonialsSection({ 
  title,
  description,
  testimonials,
  className 
}: {
  title: string;
  description: string;
  testimonials: Array<{
    author: TestimonialAuthor;
    text: string;
    href?: string;
  }>;
  className?: string;
}) {
  return (
    <section className={cn(
      "bg-background text-foreground",
      "py-12 sm:py-24 md:py-32 px-0",
      className
    )}>
      <div className="mx-auto flex max-w-7xl flex-col items-center gap-4 text-center sm:gap-16">
        <div className="flex flex-col items-center gap-4 px-4 sm:gap-8">
          <h2 className="max-w-[720px] text-3xl font-semibold leading-tight sm:text-5xl sm:leading-tight">
            {title}
          </h2>
          <p className="text-md max-w-[600px] font-medium text-muted-foreground sm:text-xl">
            {description}
          </p>
        </div>

        <div className="relative flex w-full flex-col items-center justify-center overflow-hidden">
          <div className="group flex overflow-hidden p-2 [--gap:1rem] [gap:var(--gap)] flex-row [--duration:40s]">
            <div className="flex shrink-0 justify-around [gap:var(--gap)] animate-marquee flex-row group-hover:[animation-play-state:paused]">
              {[...Array(4)].map((_, setIndex) => (
                testimonials.map((testimonial, i) => (
                  <TestimonialCard 
                    key={`${setIndex}-${i}`}
                    {...testimonial}
                  />
                ))
              ))}
            </div>
          </div>

          <div className="pointer-events-none absolute inset-y-0 left-0 hidden w-1/3 bg-gradient-to-r from-background sm:block" />
          <div className="pointer-events-none absolute inset-y-0 right-0 hidden w-1/3 bg-gradient-to-l from-background sm:block" />
        </div>
      </div>
    </section>
  );
}

// Navigation Bar Component
function NavBar({ items, className }: NavBarProps) {
  const [activeTab, setActiveTab] = useState(items[0].name);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return (
    <div
      className={cn(
        "fixed bottom-0 sm:top-0 left-1/2 -translate-x-1/2 z-50 mb-6 sm:pt-6",
        className,
      )}
    >
      <div className="flex items-center gap-3 bg-background/5 border border-border backdrop-blur-lg py-1 px-1 rounded-full shadow-lg">
        {items.map((item) => {
          const Icon = item.icon;
          const isActive = activeTab === item.name;

          return (
            <a
              key={item.name}
              href={item.url}
              onClick={() => setActiveTab(item.name)}
              className={cn(
                "relative cursor-pointer text-sm font-semibold px-6 py-2 rounded-full transition-colors",
                "text-foreground/80 hover:text-primary",
                isActive && "bg-muted text-primary",
              )}
            >
              <span className="hidden md:inline">{item.name}</span>
              <span className="md:hidden">
                <Icon size={18} strokeWidth={2.5} />
              </span>
              {isActive && (
                <motion.div
                  layoutId="lamp"
                  className="absolute inset-0 w-full bg-primary/5 rounded-full -z-10"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                  }}
                >
                  <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-primary rounded-t-full">
                    <div className="absolute w-12 h-6 bg-primary/20 rounded-full blur-md -top-2 -left-2" />
                    <div className="absolute w-8 h-6 bg-primary/20 rounded-full blur-md -top-1" />
                    <div className="absolute w-4 h-4 bg-primary/20 rounded-full blur-sm top-0 left-2" />
                  </div>
                </motion.div>
              )}
            </a>
          );
        })}
      </div>
    </div>
  );
}

// Contact Section Component
function ContactSection({
  title = "Liên hệ với chúng tôi",
  description = "Chúng tôi sẵn sàng tư vấn và hỗ trợ bạn phát triển giải pháp AI và SmartHome phù hợp nhất.",
  phone = "(+84) 123-456-789",
  email = "<EMAIL>",
  web = { label: "aismarthome.vn", url: "https://aismarthome.vn" },
}: {
  title?: string;
  description?: string;
  phone?: string;
  email?: string;
  web?: { label: string; url: string };
}) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Họ tên là bắt buộc';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email là bắt buộc';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Vui lòng nhập email hợp lệ';
    }
    
    if (!formData.message.trim()) {
      newErrors.message = 'Tin nhắn là bắt buộc';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Tin nhắn phải có ít nhất 10 ký tự';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  return (
    <section className="py-32 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="mx-auto flex max-w-screen-xl flex-col justify-between gap-10 lg:flex-row lg:gap-20">
          <div className="mx-auto flex max-w-sm flex-col justify-between gap-10">
            <div className="text-center lg:text-left">
              <h1 className="mb-2 text-5xl font-semibold lg:mb-1 lg:text-6xl text-slate-900">
                {title}
              </h1>
              <p className="text-slate-600">{description}</p>
            </div>
            <div className="mx-auto w-fit lg:mx-0">
              <h3 className="mb-6 text-center text-2xl font-semibold lg:text-left text-slate-900">
                Thông tin liên hệ
              </h3>
              <ul className="ml-4 list-disc space-y-2">
                <li className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-blue-600" />
                  <span className="font-bold">Điện thoại: </span>
                  <a href={`tel:${phone}`} className="text-blue-600 hover:underline">
                    {phone}
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span className="font-bold">Email: </span>
                  <a href={`mailto:${email}`} className="text-blue-600 hover:underline">
                    {email}
                  </a>
                </li>
                <li className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-600" />
                  <span className="font-bold">Website: </span>
                  <a href={web.url} target="_blank" className="text-blue-600 hover:underline">
                    {web.label}
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="mx-auto flex max-w-screen-md flex-col gap-6 rounded-lg border border-slate-200 bg-white p-10 shadow-lg">
            <AnimatePresence mode="wait">
              {!isSubmitted ? (
                <motion.form
                  key="form"
                  onSubmit={handleSubmit}
                  className="space-y-6"
                  initial={{ opacity: 1 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex gap-4">
                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="name">Họ và tên</Label>
                      <Input 
                        type="text" 
                        id="name" 
                        placeholder="Nhập họ và tên"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className={errors.name ? 'border-red-500' : ''}
                      />
                      {errors.name && (
                        <p className="text-red-500 text-sm">{errors.name}</p>
                      )}
                    </div>
                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="phone">Số điện thoại</Label>
                      <Input 
                        type="tel" 
                        id="phone" 
                        placeholder="Nhập số điện thoại"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid w-full items-center gap-1.5">
                    <Label htmlFor="email">Email</Label>
                    <Input 
                      type="email" 
                      id="email" 
                      placeholder="Nhập địa chỉ email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className={errors.email ? 'border-red-500' : ''}
                    />
                    {errors.email && (
                      <p className="text-red-500 text-sm">{errors.email}</p>
                    )}
                  </div>
                  <div className="grid w-full gap-1.5">
                    <Label htmlFor="message">Tin nhắn</Label>
                    <Textarea 
                      placeholder="Mô tả nhu cầu của bạn về AI và SmartHome..." 
                      id="message"
                      rows={6}
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      className={errors.message ? 'border-red-500' : ''}
                    />
                    {errors.message && (
                      <p className="text-red-500 text-sm">{errors.message}</p>
                    )}
                  </div>
                  <Button 
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                        Đang gửi...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Send className="h-4 w-4" />
                        Gửi tin nhắn
                      </div>
                    )}
                  </Button>
                </motion.form>
              ) : (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-12"
                >
                  <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold text-slate-900 mb-4">Gửi thành công!</h3>
                  <p className="text-slate-600 mb-6">
                    Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi trong vòng 24 giờ.
                  </p>
                  <Button
                    onClick={() => {
                      setIsSubmitted(false);
                      setFormData({ name: '', email: '', phone: '', message: '' });
                    }}
                    variant="outline"
                  >
                    Gửi tin nhắn khác
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  );
}

// Hero Section Component
function HeroSection() {
  const COLORS_TOP = ["#3B82F6", "#1E40AF", "#1D4ED8", "#2563EB"];
  const color = useMotionValue(COLORS_TOP[0]);

  useEffect(() => {
    animate(color, COLORS_TOP, {
      ease: "easeInOut",
      duration: 10,
      repeat: Infinity,
      repeatType: "mirror",
    });
  }, [color]);

  const backgroundImage = useMotionTemplate`radial-gradient(125% 125% at 50% 0%, #0F172A 50%, ${color})`;
  const border = useMotionTemplate`1px solid ${color}`;
  const boxShadow = useMotionTemplate`0px 4px 24px ${color}`;

  return (
    <motion.section
      style={{ backgroundImage }}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-slate-900 px-4 py-24 text-white"
    >
      <div className="relative z-10 flex flex-col items-center text-center max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="mb-6"
        >
          <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30 px-4 py-2">
            ✨ Công nghệ AI & SmartHome hàng đầu
          </Badge>
        </motion.div>

        <motion.h1
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="text-4xl sm:text-6xl md:text-7xl font-bold mb-8 bg-gradient-to-br from-white to-blue-200 bg-clip-text text-transparent leading-tight"
        >
          Phát triển phần mềm
          <br />
          <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
            AI & SmartHome
          </span>
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-xl sm:text-2xl text-blue-100 max-w-4xl mb-12 leading-relaxed"
        >
          Chúng tôi chuyên phát triển các giải pháp AI thông minh và hệ thống SmartHome hiện đại, 
          giúp khách hàng cá nhân tối ưu hóa cuộc sống và nâng cao chất lượng sống.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-6 items-center"
        >
          <motion.button
            style={{ border, boxShadow }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="group relative flex items-center gap-3 rounded-full bg-blue-600 hover:bg-blue-700 px-8 py-4 text-white font-semibold transition-all"
          >
            Tư vấn miễn phí
            <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-1" />
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center gap-3 rounded-full border border-white/20 bg-white/10 backdrop-blur-sm px-8 py-4 text-white font-semibold hover:bg-white/20 transition-all"
          >
            Xem dự án
            <Eye className="h-5 w-5" />
          </motion.button>
        </motion.div>
      </div>

      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyan-500/10 rounded-full blur-3xl animate-pulse" />
      </div>
    </motion.section>
  );
}

// Features Section Component
function FeaturesSection() {
  const features = [
    {
      icon: Brain,
      title: "Trí tuệ nhân tạo",
      description: "Phát triển các ứng dụng AI thông minh, học máy và xử lý ngôn ngữ tự nhiên.",
      gradient: "from-purple-500 to-pink-500"
    },
    {
      icon: Smartphone,
      title: "SmartHome IoT",
      description: "Hệ thống nhà thông minh tích hợp IoT, điều khiển từ xa qua smartphone.",
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      icon: Wifi,
      title: "Kết nối thông minh",
      description: "Tích hợp các thiết bị thông minh với khả năng kết nối ổn định và bảo mật.",
      gradient: "from-green-500 to-emerald-500"
    },
    {
      icon: Settings,
      title: "Tự động hóa",
      description: "Tự động hóa các tác vụ hàng ngày, tiết kiệm thời gian và năng lượng.",
      gradient: "from-orange-500 to-red-500"
    },
    {
      icon: BarChart3,
      title: "Phân tích dữ liệu",
      description: "Thu thập và phân tích dữ liệu để tối ưu hóa hiệu suất và trải nghiệm.",
      gradient: "from-indigo-500 to-purple-500"
    },
    {
      icon: Lock,
      title: "Bảo mật cao",
      description: "Đảm bảo an toàn dữ liệu với các biện pháp bảo mật tiên tiến nhất.",
      gradient: "from-gray-500 to-slate-500"
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold text-slate-900 mb-6">
            Dịch vụ của chúng tôi
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Chúng tôi cung cấp các giải pháp công nghệ tiên tiến, từ AI đến SmartHome, 
            giúp bạn xây dựng cuộc sống thông minh và hiện đại.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -5 }}
              className="group p-8 bg-white rounded-2xl border border-slate-200 shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className="w-full h-full text-white" />
              </div>
              <h3 className="text-2xl font-bold text-slate-900 mb-4">{feature.title}</h3>
              <p className="text-slate-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Proof Section Component
function ProofSection() {
  const stats = [
    { number: "50+", label: "Dự án hoàn thành", icon: Award },
    { number: "100+", label: "Khách hàng hài lòng", icon: Users },
    { number: "99%", label: "Tỷ lệ thành công", icon: TrendingUp },
    { number: "24/7", label: "Hỗ trợ khách hàng", icon: Headphones }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold text-slate-900 mb-6">
            Thành tích của chúng tôi
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Những con số ấn tượng chứng minh chất lượng dịch vụ và sự tin tưởng từ khách hàng.
          </p>
        </motion.div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center p-8 bg-white rounded-2xl shadow-lg"
            >
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <stat.icon className="w-8 h-8 text-blue-600" />
              </div>
              <div className="text-4xl font-bold text-slate-900 mb-2">{stat.number}</div>
              <div className="text-slate-600">{stat.label}</div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Footer Component
function Footer() {
  return (
    <footer className="bg-slate-900 text-white py-16">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-bold mb-4">AI & SmartHome Solutions</h3>
            <p className="text-slate-300 mb-6 max-w-md">
              Chúng tôi chuyên phát triển các giải pháp AI và SmartHome tiên tiến, 
              giúp khách hàng cá nhân tối ưu hóa cuộc sống thông minh.
            </p>
            <div className="flex space-x-4">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <Mail className="w-5 h-5" />
              </div>
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <Phone className="w-5 h-5" />
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Dịch vụ</h4>
            <ul className="space-y-2 text-slate-300">
              <li>Phát triển AI</li>
              <li>SmartHome IoT</li>
              <li>Tự động hóa</li>
              <li>Tư vấn công nghệ</li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4">Liên hệ</h4>
            <ul className="space-y-2 text-slate-300">
              <li>Email: <EMAIL></li>
              <li>Phone: (+84) 123-456-789</li>
              <li>Website: aismarthome.vn</li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-slate-700 mt-12 pt-8 text-center text-slate-400">
          <p>&copy; 2024 AI & SmartHome Solutions. Tất cả quyền được bảo lưu.</p>
        </div>
      </div>
    </footer>
  );
}

// Main Website Component
function AISmartHomeWebsite() {
  const navItems = [
    { name: 'Trang chủ', url: '#home', icon: Home },
    { name: 'Dịch vụ', url: '#features', icon: Briefcase },
    { name: 'Đánh giá', url: '#testimonials', icon: Star },
    { name: 'Liên hệ', url: '#contact', icon: Mail }
  ];

  const testimonials = [
    {
      author: {
        name: "Nguyễn Văn An",
        handle: "@nguyenvanan",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
      },
      text: "Hệ thống SmartHome do công ty phát triển rất tuyệt vời. Tôi có thể điều khiển mọi thứ trong nhà từ xa một cách dễ dàng.",
    },
    {
      author: {
        name: "Trần Thị Bình",
        handle: "@tranthibinh",
        avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face"
      },
      text: "Ứng dụng AI giúp tôi quản lý công việc hiệu quả hơn rất nhiều. Đội ngũ hỗ trợ cũng rất chuyên nghiệp.",
    },
    {
      author: {
        name: "Lê Minh Cường",
        handle: "@leminhcuong",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
      },
      text: "Giải pháp tự động hóa nhà thông minh đã thay đổi hoàn toàn cách tôi sống. Tiết kiệm được rất nhiều thời gian và năng lượng.",
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <NavBar items={navItems} />
      
      <section id="home">
        <HeroSection />
      </section>
      
      <section id="features">
        <FeaturesSection />
      </section>
      
      <section id="proof">
        <ProofSection />
      </section>
      
      <section id="testimonials">
        <TestimonialsSection
          title="Khách hàng nói gì về chúng tôi"
          description="Hàng trăm khách hàng đã tin tưởng và hài lòng với dịch vụ của chúng tôi"
          testimonials={testimonials}
        />
      </section>
      
      <section id="contact">
        <ContactSection />
      </section>
      
      <Footer />

      <style jsx global>{`
        @keyframes marquee {
          from { transform: translateX(0) }
          to { transform: translateX(calc(-100% - var(--gap))) }
        }
        
        .animate-marquee {
          animation: marquee var(--duration) linear infinite;
        }
        
        :root {
          --background: 0 0% 100%;
          --foreground: 240 10% 3.9%;
          --muted: 240 4.8% 95.9%;
          --muted-foreground: 240 3.8% 46.1%;
          --border: 240 5.9% 90%;
          --primary: 221.2 83.2% 53.3%;
        }
        
        .dark {
          --background: 240 10% 3.9%;
          --foreground: 0 0% 98%;
          --muted: 240 3.7% 15.9%;
          --muted-foreground: 240 5% 64.9%;
          --border: 240 3.7% 15.9%;
          --primary: 217.2 91.2% 59.8%;
        }
      `}</style>
    </div>
  );
}